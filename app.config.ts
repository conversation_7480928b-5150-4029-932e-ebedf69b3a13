import { ConfigContext, ExpoConfig } from '@expo/config';
import 'dotenv/config';

// Get environment variables from process.env or EAS build profile
const APP_VARIANT = process.env.APP_VARIANT || 'production';
const NODE_ENV = process.env.NODE_ENV;

// Determine environment based on APP_VARIANT
const IS_DEV = APP_VARIANT === 'development';
const IS_PREVIEW = APP_VARIANT === 'preview';
const IS_PRODUCTION = APP_VARIANT === 'production' || (!IS_DEV && !IS_PREVIEW);

// Define IOS_URL_SCHEME directly instead of importing
const IOS_URL_SCHEME = process.env.EXPO_PUBLIC_IOS_URL_SCHEME;

if (!process.env.EXPO_PUBLIC_IOS_URL_SCHEME) {
  console.warn('⚠️ Missing EXPO_PUBLIC_IOS_URL_SCHEME env variable!');
}

// Add debug logging to help troubleshoot
console.log('Environment variables:', {
  APP_VARIANT,
  NODE_ENV,
  IS_DEV,
  IS_PREVIEW,
  IS_PRODUCTION,
});

const SLUG = 'shareikna';

// Instead of hardcoding paths to service files, use environment variables
const GOOGLE_SERVICE_FILE =
  process.env.GOOGLE_SERVICE_FILE ||
  (IS_DEV
    ? './google-services.dev.json'
    : IS_PREVIEW
      ? './google-services-preview.json'
      : './google-services.json');

// console.log(`Using Google Services file: ${GOOGLE_SERVICE_FILE}`);

const getUniqueIdentifier = () => {
  if (IS_DEV) return 'com.Shareiknaexpo.dev';
  if (IS_PREVIEW) return 'com.Shareiknaexpo.preview';
  return 'com.Shareiknaexpo';
};

const getAppName = () => {
  if (IS_DEV) return 'Shareikna (Dev)';
  if (IS_PREVIEW) return 'Shareikna (Preview)';
  return 'Shareikna';
};

console.log({
  IOS_URL_SCHEME: IOS_URL_SCHEME,
  environment: {
    isDev: IS_DEV,
    isPreview: IS_PREVIEW,
    isProduction: IS_PRODUCTION,
  },
});

export default ({ config }: ConfigContext): ExpoConfig => ({
  ...config,
  name: getAppName(),
  slug: SLUG,
  version: '1.0.1',
  orientation: 'portrait',
  icon: './assets/icon.png',
  userInterfaceStyle: 'automatic',
  newArchEnabled: true,
  platforms: ['ios', 'android', 'web'],
  scheme: 'shareikna',
  ios: {
    supportsTablet: true,
    bundleIdentifier: getUniqueIdentifier(),
    config: {
      googleMapsApiKey: process.env.EXPO_PUBLIC_MAP_API_IOS_KEY,
    },
    infoPlist: {
      ITSAppUsesNonExemptEncryption: false,
      CFBundleURLTypes: [
        {
          CFBundleURLSchemes: ['shareikna', IOS_URL_SCHEME],
        },
      ],
      UIBackgroundModes: ['remote-notification'],
    },
    associatedDomains: ['applinks:shareikna.com'],
  },
  android: {
    googleServicesFile: GOOGLE_SERVICE_FILE,
    splash: {
      image: './assets/splash.png',
      resizeMode: 'cover',
      backgroundColor: '#7A26B1',
    },
    config: {
      googleMaps: {
        apiKey: process.env.EXPO_PUBLIC_MAP_API_ANDROID_KEY,
      },
    },
    adaptiveIcon: {
      foregroundImage: './assets/adaptive-icon.png',
      backgroundColor: '#ffffff',
    },
    permissions: [
      'android.permission.ACCESS_COARSE_LOCATION',
      'android.permission.ACCESS_FINE_LOCATION',
      'android.permission.CAMERA',
      'android.permission.RECORD_AUDIO',
      'android.permission.READ_EXTERNAL_STORAGE',
      'android.permission.WRITE_EXTERNAL_STORAGE',
      'android.permission.ACCESS_MEDIA_LOCATION',
      'android.permission.RECEIVE_BOOT_COMPLETED',
      'android.permission.VIBRATE',
    ],
    package: getUniqueIdentifier(),
    intentFilters: [
      {
        action: 'VIEW',
        autoVerify: true,
        data: [
          {
            scheme: 'shareikna',
          },
        ],
        category: ['BROWSABLE', 'DEFAULT'],
      },
    ],
  },
  web: {
    bundler: 'metro',
    output: 'static',
    favicon: './assets/icon.png',
  },
  plugins: [
    [
      'expo-build-properties',
      {
        android: {
          compileSdkVersion: 35,
          targetSdkVersion: 35,
          buildToolsVersion: '35.0.0',
        },
        ios: {
          deploymentTarget: '15.1',
        },
      },
    ],
    'expo-router',
    [
      'expo-splash-screen',
      {
        image: './assets/splash.png',
        resizeMode: 'contain',
        width: 200,
        backgroundColor: '#7A26B1',
      },
    ],
    [
      'expo-secure-store',
      {
        configureAndroidBackup: true,
        faceIDPermission:
          'Allow $(PRODUCT_NAME) to access your Face ID biometric data.',
      },
    ],
    'expo-localization',
    [
      'expo-location',
      {
        locationAlwaysAndWhenInUsePermission:
          'Allow $(PRODUCT_NAME) to use your location.',
      },
    ],
    [
      'react-native-vision-camera',
      {
        cameraPermissionText: '$(PRODUCT_NAME) needs access to your Camera.',
        enableMicrophonePermission: true,
        microphonePermissionText:
          '$(PRODUCT_NAME) needs access to your Microphone.',
      },
    ],
    [
      '@react-native-google-signin/google-signin',
      {
        iosUrlScheme: IOS_URL_SCHEME,
      },
    ],
    [
      'expo-media-library',
      {
        photosPermission: 'Allow $(PRODUCT_NAME) to access your photos.',
        savePhotosPermission: 'Allow $(PRODUCT_NAME) to save photos.',
        isAccessMediaLocationEnabled: true,
      },
    ],
    [
      'expo-image-picker',
      {
        photosPermission:
          'The app accesses your photos to let you share them with your friends.',
      },
    ],
    [
      'expo-video',
      {
        supportsBackgroundPlayback: true,
        supportsPictureInPicture: true,
      },
    ],
    [
      'expo-notifications',
      {
        icon: './assets/icon.png',
        color: '#ffffff',
      },
    ],
  ],
  experiments: {
    typedRoutes: true,
  },
  extra: {
    MAP_API_IOS_KEY: process.env.EXPO_PUBLIC_MAP_API_IOS_KEY,
    MAP_API_ANDROID_KEY: process.env.EXPO_PUBLIC_MAP_API_ANDROID_KEY,
    router: {
      origin: false,
    },
    eas: {
      projectId: 'fc516790-a64f-4559-920f-fae2946655d5',
    },
  },
  owner: 'shareikna-org',
});
