import { View } from 'react-native';
import React, {
  useCallback,
  useEffect,
  useMemo,
  useState,
  SetStateAction,
} from 'react';
import { useLocalSearchParams } from 'expo-router';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';

import { timeAgo } from '@/utils/formatDates';
import { IPost } from '@/types';

import { useSession } from '@/context/AuthContext';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import ModalGalleryList from '@/components/ui/ModalGalleryList';
import useGetPostById from '@/hooks/postsHooks/useGetPostById';
import SocialCircleAdminHeader from '@/components/SocialCircleAdminHeader';
import useToggleSinglePostLikes from '@/hooks/postsHooks/useToggleSinglePostLikes';
import OptimizedPostCommentList from '@/layouts/OptimizedPostCommentList';
import CustomKeyboardAvoidingView from '@/components/ui/CustomKeyboardAvoidingView';
import PostsCard from '@/components/PostsCard';

// Separate PostCardMemo component to prevent re-renders
const PostCardMemo = React.memo(
  ({
    post,
    isLiked,
    handleToggleLike,
    showImageModal,
    postId,
  }: {
    post: IPost;
    isLiked: boolean;
    handleToggleLike: () => void;
    showImageModal: () => void;
    postId: string;
  }) => {
    return (
      <PostsCard
        name={post.content}
        title={post.content}
        images={post.image}
        userId={post.postedByUserId._id}
        variant='circle'
        isLiked={isLiked}
        onLikePress={handleToggleLike}
        cover={post.image[0]}
        showImageModal={showImageModal}
        createdAt={timeAgo(post.createdAt)}
        likesCount={Number(post.likes.likesCount)}
        commentsCount={Number(post.comments?.length || 0)}
      >
        {/* Comments List */}
        <View className='px-2'>
          <OptimizedPostCommentList postId={postId} isDark={false} />
        </View>
      </PostsCard>
    );
  },
  (prevProps, nextProps) => {
    // Custom comparison to prevent unnecessary re-renders
    return (
      prevProps.isLiked === nextProps.isLiked &&
      prevProps.post.likes.likesCount === nextProps.post.likes.likesCount &&
      prevProps.post.comments?.length === nextProps.post.comments?.length
    );
  },
);

// Separate header component
const PostHeader = React.memo(
  ({
    isPostOwner,
    circleImage,
    circleName,
  }: {
    isPostOwner: boolean;
    circleImage: string;
    circleName: string;
  }) => {
    return (
      <>
        {!isPostOwner && (
          <CustomHeader
            title='Post'
            textStyles={{ fontSize: 24 }}
            href='/(protected)/(tabs)/socialCircles'
          />
        )}

        {!!isPostOwner && (
          <SocialCircleAdminHeader
            image={circleImage}
            name={circleName || 'Public'}
            participantsNumber={0}
          />
        )}
      </>
    );
  },
);

const SocialCirclePost = () => {
  const inset = useSafeAreaInsets();
  const { id, circleImage, circleName } = useLocalSearchParams<{
    id: string;
    circleId: string;
    circleImage: string;
    circleName: string;
  }>();
  const { user } = useSession();
  const [showModal, setShowModal] = useState(false);
  const { data } = useGetPostById(id);

  // Use useMemo to prevent unnecessary state updates
  const [post, setPost] = useState<IPost | null>(null);
  const [isLiked, setIsLiked] = useState(false);

  // Memoize derived values
  const isPostOwner = useMemo(() => {
    if (!user || !post) return false;
    return user._id === post.postedByUserId._id;
  }, [user?._id, post?.postedByUserId._id]);

  // Memoize toggle like handler
  const safeSetPost = useCallback((update: SetStateAction<IPost>) => {
    setPost((prev) =>
      prev ? (update instanceof Function ? update(prev) : update) : null,
    );
  }, []);

  const { handleToggleLike } = useToggleSinglePostLikes(safeSetPost);

  const toggleLike = useCallback(() => {
    handleToggleLike();
  }, [handleToggleLike]);

  const toggleModal = useCallback(() => {
    setShowModal((prev) => !prev);
  }, []);

  // Update post data when API response changes
  useEffect(() => {
    if (data?.data) {
      setPost(data.data);
    }
  }, [data?.data]);

  // Update like status when post changes
  useEffect(() => {
    if (post && user?._id) {
      setIsLiked(post.likes.userId.includes(user._id));
    }
  }, [post?.likes.userId, user?._id]);

  if (!post) return null;

  return (
    <ScreenTemplate withoutSafeAria>
      {showModal && (
        <View className='flex-1 absolute inset-0 bg-black'>
          <ModalGalleryList
            images={post.image || []}
            onClose={() => setShowModal(false)}
            visible={showModal}
          />
        </View>
      )}

      <View className='pb-3' style={{ paddingTop: inset.top }}>
        <PostHeader
          isPostOwner={isPostOwner}
          circleImage={circleImage}
          circleName={circleName}
        />
      </View>

      <CustomKeyboardAvoidingView>
        <View className='px-2'>
          <PostCardMemo
            post={post}
            isLiked={isLiked}
            handleToggleLike={toggleLike}
            showImageModal={toggleModal}
            postId={post._id}
          />
        </View>
      </CustomKeyboardAvoidingView>
    </ScreenTemplate>
  );
};

export default SocialCirclePost;
