import React, { useCallback } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { Image } from 'expo-image';

import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import CustomInput from '@/components/ui/CustomInput';
import CustomButton from '@/components/ui/buttons/CustomButton';
import useCreateGroupChatRoom from '@/hooks/chatHooks/useCreateGroupChatRoom';
import useImagePicker from '@/hooks/useImagePicker';
import { useSession } from '@/context/AuthContext';
import { APP_Icons } from '@/constants/Images';

const createGroupSchema = z.object({
  groupName: z
    .string({ message: 'You must enter a group name.' })
    .min(1, { message: 'Group name must be at least 1 character long.' }),
});

type CreateGroupSchema = z.infer<typeof createGroupSchema>;

const CreateGroupMessage = () => {
  const { user } = useSession();
  const { pickImage, result } = useImagePicker({
    imagePickOptions: { allowsEditing: false, selectionLimit: 1 },
  });

  const { mutate, isPending } = useCreateGroupChatRoom();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateGroupSchema>({
    defaultValues: { groupName: '' },
    resolver: zodResolver(createGroupSchema),
  });

  const handleCreateGroup = useCallback(
    (data: CreateGroupSchema) => {
      if (!user?._id) return;

      mutate(
        {
          title: data.groupName,
          members: [user._id],
          image: result?.[0]?.uri ?? '',
        },
        {
          onSuccess: () => {
            router.replace('/(protected)/(tabs)/messages/group');
          },
        },
      );
    },
    [user, result, mutate],
  );

  return (
    <ScreenTemplate>
      <View className='flex-1 px-4 py-6 gap-6'>
        <CustomHeader title='Create Group' />

        {/* Group Avatar Picker */}
        <View className='items-center'>
          <TouchableOpacity
            className='w-24 h-24 rounded-full bg-neutral-200 items-center justify-center overflow-hidden'
            onPress={pickImage}
          >
            {result?.[0]?.uri ? (
              <Image
                source={{ uri: result[0].uri }}
                style={{ width: '100%', height: '100%' }}
                contentFit='cover'
              />
            ) : (
              <APP_Icons.GalleryIcon width={40} height={40} />
            )}
          </TouchableOpacity>
          <Text className='mt-2 text-sm text-neutral-500'>
            Tap to add image
          </Text>
        </View>

        {/* Group Name Input */}
        <CustomInput
          placeholder='Enter group name'
          name='groupName'
          control={control}
          error={errors.groupName?.message}
        />

        {/* Submit Button */}
        <CustomButton
          onPress={handleSubmit(handleCreateGroup)}
          disabled={isPending}
          className='mt-2'
        >
          {isPending ? 'Creating...' : 'Create Group'}
        </CustomButton>
      </View>
    </ScreenTemplate>
  );
};

export default CreateGroupMessage;
