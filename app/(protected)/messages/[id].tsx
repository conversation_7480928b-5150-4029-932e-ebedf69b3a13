import { useEffect, useRef, useState, useCallback, useMemo } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { useLocalSearchParams } from 'expo-router';
import ChatHeader from '@/components/chatComponents/ChatHeader';
import {
  FlatList,
  View,
  TextInput,
  StyleSheet,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
  Keyboard,
} from 'react-native';
import { useSession } from '@/context/AuthContext';
import ChatBubble from '@/components/chatComponents/ChatBubble';
import useGetChatHistory from '@/hooks/chatHooks/useGetChatHistory';
import ChatInputAndUpload from '@/components/chatComponents/ChatInputAndUpload';
import { useSocket } from '@/context/SocketContext';
import { IMessage } from '@/types';
import { COLORS } from '@/constants/Theme';

import {
  withTiming,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';

/*
## For group chat we use receiveGroupMessage to receive messages from group 
## For single 1 to 1 chat we use  receiveMessage to receive the messages 
## api works paginated as we scroll we load more 

*/

// receiveMessage
// receiveGroupMessage

// Extended message type to include user info needed for UI
interface ExtendedMessage extends IMessage {
  user: {
    _id: string;
    displayName: string;
    image?: string;
    username?: string;
  };
  seen?: boolean;
  pending?: boolean;
}

const MessageByID = () => {
  // id is for chat room id for group or user id if single
  // title is the title of group chat or user display name if single

  const { id, type, image, title, isOnline } = useLocalSearchParams<{
    id: string;
    image?: string;
    title: string;
    type: 'single' | 'group';
    isOnline: string;
  }>();

  const { user: loggedInUser } = useSession();
  const [inputText, setInputText] = useState('');
  const inputRef = useRef<TextInput>(null);
  const [messages, setMessages] = useState<ExtendedMessage[]>([]);
  const flatListRef = useRef<FlatList>(null);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isSearching, setIsSearching] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  const searchBarHeight = useSharedValue(0);
  const searchBarOpacity = useSharedValue(0);

  const recipientId = id;

  // Use our socket context
  const { isConnected, addEventListener, emitEvent } = useSocket();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, refetch } =
    useGetChatHistory({
      recipientId: recipientId,
      limit: 20, // Start with a smaller batch
    });

  // Handle loading more messages
  const handleLoadMore = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage && !isLoadingMore) {
      setIsLoadingMore(true);
      fetchNextPage().finally(() => {
        setIsLoadingMore(false);
      });
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage, isLoadingMore]);

  const getUserInfo = (userId: string) => {
    const isCurrentUser = userId === loggedInUser?._id;

    if (isCurrentUser) {
      return {
        _id: loggedInUser?._id || '',
        displayName: loggedInUser?.displayName || 'You',
        image: loggedInUser?.image || '',
        username: loggedInUser?.username || '',
      };
    } else {
      return {
        _id: id,
        displayName: title,
        image: image,
        username: title || '',
      };
    }
  };

  const filteredMessages = useMemo(() => {
    if (!searchQuery.trim()) return messages;

    return messages.filter((message) =>
      message.text.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [messages, searchQuery]);

  // Set initial messages from API data
  useEffect(() => {
    if (data?.pages) {
      const apiMessages: ExtendedMessage[] = data.pages.flatMap((page) =>
        page.data.map((item) => ({
          _id: `${item.senderId}-${item.recipientId}-${item.createdAt}`,
          createdAt: new Date(item.createdAt),
          text: item.messageText,
          senderId: item.senderId,
          recipientId: item.recipientId,
          user: getUserInfo(item.senderId),
          seen: item.isRead,
        })),
      );
      setMessages(apiMessages);
    }
  }, [data]);

  // Set up socket event listeners
  useEffect(() => {
    // Handle incoming messages
    const handleReceiveMessage = (data: any) => {
      // Create a unique ID for the message
      const messageId = `${data.senderId}-${data.recipientId}-${new Date(data.createdAt || Date.now()).toISOString()}`;

      // Check if this is our own message coming back from the server
      const isOwnMessageEcho =
        data.senderId === loggedInUser?._id &&
        messages.some(
          (msg) =>
            msg.text === data.messageText &&
            new Date(msg.createdAt).getTime() > Date.now() - 5000,
        );

      // Only add the message if it's not our own message echo
      if (!isOwnMessageEcho) {
        const newMessage: ExtendedMessage = {
          _id: messageId,
          createdAt: new Date(data.createdAt || Date.now()),
          text: data.messageText,
          senderId: data.senderId,
          recipientId: data.recipientId,
          user: getUserInfo(data.senderId),
          isRead: false,
        };

        setMessages((prev) => [newMessage, ...prev]);
      }
    };

    // Add event listener for receiving messages
    const cleanup = addEventListener('receiveMessage', handleReceiveMessage);

    // Clean up when component unmounts
    return cleanup;
  }, [addEventListener, loggedInUser?._id, messages]);

  // Add keyboard handling
  useEffect(() => {
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => {
        if (isSearching) {
          inputRef.current?.blur();
        }
      },
    );

    return () => {
      keyboardDidHideListener.remove();
    };
  }, [isSearching]);

  // Add scroll to match functionality
  const scrollToMatchingMessage = useCallback((index: number) => {
    if (flatListRef.current && index >= 0) {
      flatListRef.current.scrollToIndex({
        index,
        animated: true,
        viewPosition: 0.5,
      });
    }
  }, []);

  // Send message function
  const handleSendMessage = () => {
    if (!inputText.trim() || !isConnected) return;

    // Create a unique timestamp for this message
    const messageTimestamp = new Date().toISOString();
    const senderId = loggedInUser?._id || '';

    // Create message object
    const messageData = {
      senderId: senderId,
      recipientId: recipientId,
      messageText: inputText,
      senderName: loggedInUser?.displayName || 'User',
      senderImage: loggedInUser?.image || '',
      senderUsername: loggedInUser?.username || 'user',
      createdAt: messageTimestamp,
    };

    // Add message to local state immediately for UI responsiveness
    const newMessage: ExtendedMessage = {
      _id: `${senderId}-${recipientId}-${messageTimestamp}`,
      createdAt: new Date(),
      text: inputText,
      senderId: senderId,
      recipientId: recipientId,
      user: getUserInfo(senderId),
      pending: true,
    };

    setMessages((prev) => [newMessage, ...prev]);

    // Send message through socket
    emitEvent('sendMessage', messageData, (response: any) => {
      if (response && response.success) {
        setMessages((prev) =>
          prev.map((msg) =>
            msg._id === newMessage._id
              ? { ...msg, pending: false, _id: response.messageId || msg._id }
              : msg,
          ),
        );
      }
    });

    setInputText('');
  };

  const handleStartSearch = useCallback(() => {
    setIsSearching(true);
    searchBarHeight.value = withSpring(100);
    searchBarOpacity.value = withTiming(1, { duration: 200 });
  }, []);

  const handleEndSearch = useCallback(() => {
    searchBarOpacity.value = withTiming(0, { duration: 150 });
    searchBarHeight.value = withSpring(0, {}, (finished) => {
      if (finished) {
        setIsSearching(false);
        setSearchQuery('');
        Keyboard.dismiss();
      }
    });
  }, []);

  const searchBarAnimatedStyle = useAnimatedStyle(() => ({
    height: searchBarHeight.value,
    opacity: searchBarOpacity.value,
  }));

  // Render loading indicator for pagination
  const renderFooter = () => {
    if (!isFetchingNextPage) return null;

    return (
      <View className='py-4 flex items-center justify-center'>
        <ActivityIndicator size='small' color={COLORS.primary[50]} />
      </View>
    );
  };

  return (
    <ScreenTemplate withoutSafeAria>
      <ChatHeader
        user={{
          _id: id,
          displayName: title,
          image: image || '',
        }}
        isOnline={JSON.parse(isOnline)}
        lastSeen={new Date()}
        isGroup={type === 'group'}
        roomId={id}
      />

      <KeyboardAvoidingView
        style={{ flex: 1 }}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 20 : 10}
      >
        <View className='flex-1 px-2 pb-3'>
          <FlatList
            showsVerticalScrollIndicator={false}
            ref={flatListRef}
            data={filteredMessages}
            style={styles.flatList}
            contentContainerStyle={styles.flatListContent}
            inverted
            onEndReached={handleLoadMore}
            onEndReachedThreshold={0.3}
            ListFooterComponent={renderFooter}
            initialNumToRender={15}
            maxToRenderPerBatch={10}
            windowSize={10}
            keyExtractor={(item) => item._id}
            renderItem={({ item }) => (
              <ChatBubble
                text={item.text}
                isCurrentUser={item.senderId === loggedInUser?._id}
                createdAt={item.createdAt}
                user={{
                  displayName: item.user.displayName,
                  image: item.user.image,
                }}
                seen={item.seen}
                searchQuery={searchQuery}
              />
            )}
          />

          {!isSearching && (
            <ChatInputAndUpload
              handleSendMessage={handleSendMessage}
              inputText={inputText}
              setInputText={setInputText}
              ref={inputRef}
            />
          )}
        </View>
      </KeyboardAvoidingView>
    </ScreenTemplate>
  );
};

export default MessageByID;

const styles = StyleSheet.create({
  flatList: {
    flex: 1,
    paddingHorizontal: 10,
  },
  flatListContent: {
    paddingVertical: 16,
    gap: 12,
  },
});
