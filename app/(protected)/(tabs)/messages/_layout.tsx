import React, { memo } from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { router } from 'expo-router';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomText from '@/components/ui/CustomText';
import { Slot } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';
import { useMessagesStore } from '@/store/useMessagesStore';
import SearchBar from '@/layouts/SearchBar';

interface IButtonProps {
  title: string;
  isActive: boolean;
  onPress: () => void;
}

const Button = memo((props: IButtonProps) => (
  <TouchableOpacity
    onPress={props.onPress}
    style={[
      styles.tabButton,
      props.isActive ? styles.activeTabButton : styles.inactiveTabButton,
    ]}
  >
    <CustomText
      className={`font-medium text-h4 ${props.isActive ? 'text-white-50' : 'text-neutral-500'}`}
    >
      {props.title}
    </CustomText>
  </TouchableOpacity>
));

const _layout = () => {
  const { activeTab, setActiveTab } = useMessagesStore();

  const navigateToTab = (tab: 'all' | 'single' | 'group') => {
    setActiveTab(tab);
    if (tab === 'all') {
      router.replace('/(protected)/(tabs)/messages');
    } else if (tab === 'single') {
      router.replace('/(protected)/(tabs)/messages');
    } else if (tab === 'group') {
      router.replace('/(protected)/(tabs)/messages/group');
    }
  };

  return (
    <ScreenTemplate>
      <View className='flex-1'>
        {/* Custom Header */}
        <View className='px-4 py-3 flex-row items-center justify-between'>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons
              name='chevron-back'
              size={normalized(28)}
              color={COLORS.white[50]}
            />
          </TouchableOpacity>

          <CustomText className='text-white-50 text-h1 font-semibold'>
            My Messages
          </CustomText>

          <TouchableOpacity onPress={() => router.push('/messages/requests')}>
            <CustomText className='text-secondary-300 text-body2'>
              Requests
            </CustomText>
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View className='mb-3 px-2'>
          <SearchBar placeholder='Search...' />
        </View>

        {/* Custom Tab Buttons */}
        <View className='flex-row px-4 gap-3 mb-4'>
          <Button
            title='All'
            isActive={activeTab === 'all'}
            onPress={() => navigateToTab('all')}
          />
          <Button
            title='Single'
            isActive={activeTab === 'single'}
            onPress={() => navigateToTab('single')}
          />
          <Button
            title='Group'
            isActive={activeTab === 'group'}
            onPress={() => navigateToTab('group')}
          />
        </View>

        {/* Content Area */}
        <View className='flex-1'>
          <Slot />
        </View>
      </View>
    </ScreenTemplate>
  );
};

const styles = StyleSheet.create({
  tabButton: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 50,
    minWidth: 80,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: COLORS.neutral[400],
  },
  activeTabButton: {
    backgroundColor: '#8A2BE2', // Purple color from screenshot
  },
  inactiveTabButton: {
    backgroundColor: 'transparent',

    opacity: 0.5,
  },
});

export default _layout;
