import React, { useMemo } from 'react';
import { FlatList, Platform, View } from 'react-native';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import { APP_Icons } from '@/constants/Images';
import ChatListItem from '@/components/chatComponents/ChatListItem';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import { useMessagesStore } from '@/store/useMessagesStore';
import useGetAllChats from '@/hooks/chatHooks/useGetALLMessages';

const MessagesScreen = () => {
  const bottomHeigh = useBottomTabBarHeight();
  const { data } = useGetAllChats();
  const { searchQuery } = useMessagesStore();

  const usersData = useMemo(() => {
    return data?.directChats.filter((item) =>
      item.username.toLowerCase().includes(searchQuery.toLowerCase()),
    );
  }, [searchQuery, data]);

  return (
    <View className='flex-1 px-2'>
      {/* Chat List */}
      <View className='flex-1 pt-4'>
        <FlatList
          data={usersData}
          showsVerticalScrollIndicator={false}
          contentContainerClassName='gap-5'
          className={`flex-1 mb-[${Platform.OS === 'ios' ? bottomHeigh : 25}px]`}
          ItemSeparatorComponent={() => (
            <View className='border-hairline border-neutral-300/20 mt-3' />
          )}
          ListEmptyComponent={() => (
            <EmptyListComponent
              title='No Conversations Yet'
              description='Start connecting with others by joining activities or messaging users'
              emptyIcon={() => (
                <APP_Icons.EmptyMessageIcon width={70} height={70} />
              )}
            />
          )}
          renderItem={({ item }) => (
            <ChatListItem
              createdAt={new Date()}
              message={item.last_message.content}
              title={item.displayName || item.username}
              image={item.profile_picture}
              numberOfMessages={item.unseen_message_count}
              href={'/(protected)/messages/[id]'}
              roomId={item.user_id}
              type='single'
              isOnline={item.is_online}
            />
          )}
        />
      </View>
    </View>
  );
};

export default MessagesScreen;
