import React, { useCallback, useMemo, useState } from 'react';
import {
  FlatList,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import { Link } from 'expo-router';

import { COLORS, normalized, SCREEN_HEIGHT } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import { IActivityItem } from '@/types';
// utils and translation

import { getDayNumber, getMonthFromDate } from '@/utils/formatDates';
import { useTranslation } from 'react-i18next';

// Components
import Loading from '@/layouts/Loading';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomText from '@/components/ui/CustomText';
import CustomButton from '@/components/ui/buttons/CustomButton';
import ActivityCard from '@/components/ActivityCard';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import EmptyCalender from '@/assets/icons/empty-calender.svg';
import CustomHeader from '@/components/ui/CustomHeader';
// Hooks
import useGetJoinedActivities from '@/hooks/activityHooks/useGetJoinedActivities';
import useGetCreatedActivities from '@/hooks/activityHooks/useGetCreatedActivity';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';

const ActivitiesScreen = () => {
  const { t } = useTranslation();
  const [filter, setFilter] = useState<'joined' | 'created'>('joined');
  const bottomTabBar = useBottomTabBarHeight();

  const {
    data: joinedQuery,
    refetch: joinRefetch,
    isLoading: joinQueryLoading,
    isError: joinIsError,
  } = useGetJoinedActivities({ enabled: filter === 'joined' });
  const {
    data: createdQuery,
    refetch: createdRefetch,
    isLoading: createLoading,
    isError: createIsError,
  } = useGetCreatedActivities({
    enabled: filter === 'created',
  });

  const isLoading = joinQueryLoading || createLoading;
  const isError = joinIsError || createIsError;
  // Derived state
  const isJoinedSelected = filter === 'joined';

  // Memoized activities data
  const activities: IActivityItem[] = useMemo(() => {
    return isJoinedSelected
      ? joinedQuery?.data || []
      : createdQuery?.calendar || [];
  }, [isJoinedSelected, joinedQuery, createdQuery]);

  // Filter toggle handler
  const handleFilterToggle = useCallback(
    (selectedFilter: 'joined' | 'created') => {
      setFilter(selectedFilter || 'joined');
    },
    [],
  );

  // Memoized render item
  const renderActivityItem = useCallback(
    ({ item }: { item: IActivityItem }) => {
      return (
        <Link href={`/(protected)/activity/${item._id}`} asChild>
          <Pressable>
            <ActivityCard
              cover={item.images[0]}
              day={getDayNumber(item.startDate)}
              month={getMonthFromDate(item.startDate)}
              location={item.location.address || '-'}
              title={item.name}
              currency={item.currency}
              price={Number(item.price)}
              members={item.members}
            />
          </Pressable>
        </Link>
      );
    },
    [],
  );

  return (
    <ScreenTemplate>
      <View className='flex-1 px-2 '>
        <CustomHeader title='My Activities'>
          <Link
            href='/(protected)/activity/activityRequestsScreen'
            className='text-secondary-300 text-h5 font-medium'
          >
            {t('requests')}
          </Link>
        </CustomHeader>

        {/* Create new-Activity */}
        <Link href='/(protected)/activity/createActivityScreen' asChild>
          <CustomButton
            variant='secondary'
            style={styles.createActivityButton}
            icon={<Ionicons name='add' size={24} color={COLORS.white[50]} />}
          >
            {t('create-new-activity')}
          </CustomButton>
        </Link>

        {/* Filter Toggle */}
        <View className='flex-row justify-around items-center'>
          {(['joined', 'created'] as const).map((filterType) => (
            <TouchableOpacity
              key={filterType}
              onPress={() => handleFilterToggle(filterType)}
            >
              <CustomText
                className={`text-h3 font-semibold pb-1 ${
                  filter === filterType
                    ? 'text-secondary-300'
                    : 'text-neutral-500'
                }`}
              >
                {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
              </CustomText>
            </TouchableOpacity>
          ))}
        </View>

        {/* Active Indicator */}
        <View className='h-[2px] bg-neutral-500 mb-5 relative'>
          <View
            className={`absolute bg-secondary-300 w-[48%] h-full ${
              isJoinedSelected ? 'left-0' : 'right-0'
            }`}
          />
        </View>

        {/* Loading State */}
        {isLoading && (
          <View className='flex-1 justify-center items-center'>
            <Loading isLoading />
          </View>
        )}

        {/* Activities List */}
        {!isLoading && (
          <FlatList
            data={activities}
            showsVerticalScrollIndicator={false}
            contentContainerClassName='gap-3 pb-3 '
            contentContainerStyle={{ paddingBottom: bottomTabBar }}
            initialNumToRender={5}
            maxToRenderPerBatch={5}
            windowSize={10}
            ListEmptyComponent={() => (
              <View
                className='flex-1 justify-center items-center'
                style={{ height: SCREEN_HEIGHT / 2 }}
              >
                <EmptyListComponent
                  emptyIcon={EmptyCalender}
                  description={
                    isJoinedSelected
                      ? "You haven't joined any activities yet"
                      : "You haven't created any activities yet"
                  }
                  buttonTitle='Find Activity'
                  onRefreshPress={() =>
                    isJoinedSelected ? joinRefetch() : createdRefetch()
                  }
                  isError={isError}
                  errorButtonTitle='Reload again'
                  errorTitle={'something went Wrong'}
                />
              </View>
            )}
            keyExtractor={(item) => `Activity-${item._id}`}
            renderItem={renderActivityItem}
          />
        )}
      </View>
    </ScreenTemplate>
  );
};

export default ActivitiesScreen;

const styles = StyleSheet.create({
  createActivityButton: {
    borderRadius: normalized(5),
    marginVertical: normalized(15),
    marginHorizontal: normalized(8),
  },
});
