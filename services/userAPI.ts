import { IFollower, IUser } from '@/types';
import axiosInstance from '@/utils/axiosInstance';
import { handleError } from '@/utils/errorHandler';

// Login APIS

interface ILoginByEmailAPI {
  message: string;
  success: boolean;
  token: null | string;
  user: null | IUser;
}

export const loginByEmailAPI = async ({
  email,
  password,
}: {
  email: string;
  password: string;
}) => {
  try {
    const response = await axiosInstance.post<ILoginByEmailAPI>(
      '/user/auth/login',
      {
        email,
        password,
      },
    );
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

export interface ILoginByGoogleResponse {
  success: boolean;
  message: string;
  token: string;
  user: IUser;
}

export interface ILoginByGoogleAPIpayload {
  idToken: string;
}

export const LoginByGoogleAPI = async (payload: ILoginByGoogleAPIpayload) => {
  try {
    const response = await axiosInstance.post<ILoginByGoogleResponse>(
      '/user/auth/signin/google',
      payload,
    );
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

// get userData by userAPI

interface IGetUserDataAPIResponse {
  success: boolean;
  fcmTokens: { _id: string };
  isFollower: boolean;
  isRequest: boolean;
  message: 'User details!';
  friendStatus: null | 'friend';
  isFriend: boolean;
  user: IUser;
}

export const getUserDataAPI = async (userId: string) => {
  try {
    const response = await axiosInstance.get<IGetUserDataAPIResponse>(
      `/user/auth/getUserById?id=${userId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Register APIS

export interface IRegisterByGoogleBody {
  idToken: string | null;
  user: {
    username: string | null;
    email: string | null;
    familyName: string | null;
    givenName: string | null;
    id: string | null;
    name: string | null;
    photo: string | null;
  };
}

interface ISignUpByGoogleResponse {
  success: boolean;
  message: string;
  token: string;
  user: {
    username: string;
    email: string;
  };
}

export const SignupByGoogleAPI = async (payload: IRegisterByGoogleBody) => {
  try {
    const response = await axiosInstance.post<ISignUpByGoogleResponse>(
      '/user/auth/signup/google',
      payload,
    );
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

export interface IRegisterWithEmailPayload {
  username: string;
  email: string;
  password: string;
  phoneNumber: string;
  displayName: string;
  image: string;
  dateOfBirth: string;
  gender: string;
  address: string;
  location: {
    longitude: number;
    latitude: number;
    address?: string | null;
  };
}

export interface IRegisterByEmailResponse {
  message: string;
  success: boolean;
  token: string | null;
  user: IUser;
}

export const RegisterWithEmailAPI = async (
  payload: IRegisterWithEmailPayload,
) => {
  try {
    const response = await axiosInstance.post<IRegisterByEmailResponse>(
      '/user/auth/signup',
      payload,
    );
    return response.data;
  } catch (error) {
    handleError(error);
    throw error;
  }
};

interface ISendOTPResponse {
  message: string;
  success: boolean;
  envelope: {
    from: string;
    to: string[];
  };
  OTP: string;
}

export const SendAuthOTPAPI = async ({ email }: { email: string }) => {
  try {
    const response = await axiosInstance.post<ISendOTPResponse>(
      `user/auth/sendOTP?email=${email}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

// validate the OTP for Register and reset password
export interface IValidateAuthOTPPayload {
  email: string;
  otp: string;
}

interface IValidateAuthOTPResponse {
  success: boolean;
  message: string;
}

export const ValidateAuthOTPAPI = async (payload: IValidateAuthOTPPayload) => {
  try {
    const response = await axiosInstance.post<IValidateAuthOTPResponse>(
      '/user/auth/verifyOTP',
      payload,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Reset password APIS
interface ISendEmailToForgetPasswordAPIResponse {
  success: boolean;
  message: string;
  otp: null | number;
}

export const SendEmailToForgetPasswordAPI = async ({
  email,
}: {
  email: string;
}) => {
  try {
    const response =
      await axiosInstance.post<ISendEmailToForgetPasswordAPIResponse>(
        '/user/auth/forgotPassword',
        { email },
      );
    return response.data;
  } catch (error) {
    throw error;
  }
};

// set the new password
export interface ISetNewPasswordAPIPayload {
  email: string;
  password: string;
}

interface ISetNewPasswordAPIResponse {
  success: boolean;
  message: string;
}

export const SetNewPasswordAPI = async (payload: ISetNewPasswordAPIPayload) => {
  try {
    const response = await axiosInstance.post<ISetNewPasswordAPIResponse>(
      '/user/auth/resetPassword',
      payload,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IUpdateProfileResponse = {
  user: IUser;
  message: string;
  status: number;
};
// Update user profile
// services/registerAPI.ts
export const UpdateUserProfileAPI = async (
  formData: FormData,
): Promise<IUpdateProfileResponse> => {
  try {
    const response = await axiosInstance.put<IUpdateProfileResponse>(
      '/user/auth/updateUser',
      formData,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: (data) => data, // Ensure `FormData` is sent as is
      },
    );

    return response.data;
  } catch (error: any) {
    throw new Error(
      error?.response?.data?.message || 'Failed to update user profile',
    );
  }
};

// !DELETE USER API

interface IDeleteUserAPIResponse {
  success: boolean;
  message: string;
}

export const DeleteUserAPI = async ({ userId }: { userId: string }) => {
  try {
    const response = await axiosInstance.post<IDeleteUserAPIResponse>(
      '/user/auth/deleteUser',
      {
        userId,
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

interface IUpdatePasswordAPIResponse {
  success: boolean;
  message: string;
}

/**
 * Note This API used to update the user password when he is already registered and he knows his old password  password  Not the forget password API
 */

export const updatePasswordAPI = async (payload: {
  oldPassword: string;
  newPassword: string;
}): Promise<IUpdatePasswordAPIResponse> => {
  try {
    const response = await axiosInstance.put<IUpdatePasswordAPIResponse>(
      '/user/auth/updatePassword',
      payload,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const updateUserInterestsAPI = async (formData: FormData) => {
  try {
    const response = await axiosInstance.put(
      '/user/auth/updateUser',
      formData,
      {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: (data) => data, // Ensure `FormData` is sent as is
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IGetUserFollowersAPI = {
  followers: {
    _id: string;
    displayName: string;
    email: string;
    image: string;
    username: string;
  }[];
  success: boolean;
};

export const getUserFollowersAPI = async (
  userId: string,
): Promise<IGetUserFollowersAPI> => {
  try {
    const response = await axiosInstance.get<IGetUserFollowersAPI>(
      `/user/profile/getFollowers/${userId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IGetUserFollowingsAPI = {
  following: IFollower[];
  success: boolean;
};

export const getUserFollowingAPI = async (
  userId: string,
): Promise<IGetUserFollowingsAPI> => {
  try {
    const response = await axiosInstance.get<IGetUserFollowingsAPI>(
      `/user/profile/getFollowing/${userId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Follow user

export const followUserAPI = async (userIdToFollow: string) => {
  try {
    const response = await axiosInstance.post('/user/profile/followUser', {
      userIdToFollow,
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};
export const unFollowUserAPI = async (userIdToUnfollow: string) => {
  try {
    const response = await axiosInstance.delete('/user/profile/unfollowUser', {
      data: {
        userIdToUnfollow,
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export interface ISearchUserPayload {
  page: number;
  limit: number;
  query: string;
}

export interface ISearchUserResponse {
  success: boolean;
  message: string;
  data: {
    user_id: string;
    username: string;
    display_name: string;
    profile_picture: string;
    followers_count: number;
    following_count: number;
    is_following: boolean;
  }[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

export const searchUserAPI = async (
  payload: ISearchUserPayload,
): Promise<ISearchUserResponse> => {
  try {
    const response = await axiosInstance.get<ISearchUserResponse>(
      `/user/auth/searchUser`,
      { params: payload },
    );

    return response.data;
  } catch (error) {
    throw error;
  }
};
