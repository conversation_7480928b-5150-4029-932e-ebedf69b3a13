import { IChatRoom, IDirectChats } from '@/types/chat';
import axiosInstance from '@/utils/axiosInstance';

export interface ChatHistoryParams {
  recipientId: string;
  limit?: number;
  page?: number;
}

export interface ChatMessage {
  senderId: string;
  recipientId: string;
  messageText: string;
  createdAt: Date;
  isRead: boolean;
}

export interface ChatHistoryResponse {
  success: boolean;
  message: string;
  limit: number;
  page: number; // current page
  totalPages: number;
  data: ChatMessage[];
}
// one to one chat message api to get chat history
export const getChatMessagesAPI = async ({
  recipientId,
  limit,
  page = 1,
}: ChatHistoryParams) => {
  try {
    const response = await axiosInstance.get<ChatHistoryResponse>(
      `/chat/index/getChatMessages/${recipientId}`,
      {
        params: {
          limit,
          page,
        },
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

// group chat create room
export interface createChatRoomPayload {
  title: string;
  members: string[];
  image?: string;
}

export interface ICreateChatRoomResponse {
  success: boolean;
  message: string;
  room: {
    _id: string;
    firebaseRoomId: string;
    title: string;
    image: string;
    createdBy: string;
    members: string[];
    createdAt: Date;
  };
}

export const createGroupChatAPI = async (
  payload: createChatRoomPayload,
): Promise<ICreateChatRoomResponse> => {
  try {
    const response = await axiosInstance.post<ICreateChatRoomResponse>(
      '/chat/index/createRoom',
      payload,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export interface GetAllRoomsResponse {
  success: boolean;
  message: string;
  createdRooms: IChatRoom[];
  joinedRooms: IChatRoom[];
}

// get all group chat rooms
export const getAllRoomsAPI = async (): Promise<GetAllRoomsResponse> => {
  try {
    const response = await axiosInstance.get<GetAllRoomsResponse>(
      '/chat/index/getAllRooms',
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export interface IGetAllMessagesResponse {
  success: boolean;
  message: string;
  directChats: IDirectChats[];
}

// API to get all direct chat messages
export const getAllMessagesAPI = async (): Promise<IGetAllMessagesResponse> => {
  try {
    const response = await axiosInstance.get<IGetAllMessagesResponse>(
      '/chat/index/getAllMessages',
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const deleteChatGroupAPI = async (groupId: string) => {
  try {
    const response = await axiosInstance.delete(
      `/chat/index/deleteRoom/${groupId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export interface IGroupMessagePayload {
  groupId: string;
  messageText: string;
  attachments?: {
    url: string;
    type: string;
    name: string;
  }[];
}

interface ISendGroupMessageAPIResponse {
  success: boolean;
  message: string;
}

export const sendGroupChatMessageAPI = async (
  payload: IGroupMessagePayload,
): Promise<ISendGroupMessageAPIResponse> => {
  try {
    const response = await axiosInstance.post<ISendGroupMessageAPIResponse>(
      `/chat/index/sendGroupMessage/${payload.groupId}`,
      payload,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
