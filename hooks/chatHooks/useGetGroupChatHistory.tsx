import {
  GroupChatHistoryParams,
  GroupChatHistoryResponse,
  getGroupChatMessagesAPI,
} from '@/services/ChatAPI';
import { useInfiniteQuery } from '@tanstack/react-query';

const useGetGroupChatHistory = ({
  groupId,
  limit = 20,
}: GroupChatHistoryParams) => {
  const query = useInfiniteQuery<GroupChatHistoryResponse>({
    queryKey: ['get-group-chat-history', groupId, limit],
    queryFn: async ({ pageParam = 1 }) =>
      getGroupChatMessagesAPI({ groupId, page: pageParam as number, limit }),
    getNextPageParam: (lastPage) =>
      lastPage.page < lastPage.totalPages ? lastPage.page + 1 : undefined,
    initialPageParam: 1,
    enabled: !!groupId,
  });

  return query;
};

export default useGetGroupChatHistory;
