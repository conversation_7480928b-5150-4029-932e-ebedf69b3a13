import {
  ChatHistoryParams,
  ChatHistoryResponse,
  getChatMessagesAPI,
} from '@/services/ChatAPI';
import { useInfiniteQuery } from '@tanstack/react-query';

const useGetChatHistory = ({ recipientId, limit = 20 }: ChatHistoryParams) => {
  const query = useInfiniteQuery<ChatHistoryResponse>({
    queryKey: ['get-chat-history', recipientId, limit],
    queryFn: async ({ pageParam = 1 }) =>
      getChatMessagesAPI({ recipientId, page: pageParam as number }),
    getNextPageParam: (lastPage) =>
      lastPage.page < lastPage.totalPages ? lastPage.page + 1 : undefined,
    initialPageParam: 1,
    enabled: !!recipientId,
  });

  return query;
};

export default useGetChatHistory;
