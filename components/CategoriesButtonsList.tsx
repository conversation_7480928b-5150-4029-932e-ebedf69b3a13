import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Animated,
  View,
} from 'react-native';
import React, {
  memo,
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react';
import { COLORS, normalized } from '@/constants/Theme';
import CustomText from './ui/CustomText';
import useLanguage from '@/hooks/useLanguage';
import useCategoriesQuery from '@/hooks/categoryHooks/useGetCategories';
import { useCategoriesStore } from '@/stores/useCategoriesStore';
import { ICategory } from '@/types';
import { Image } from 'expo-image';

/*
component for showing HomeScreen categories Buttons 
1- Render item returns Animated button to select category 
## The animated button is a Animated touchable opacity and Custom text and icon  
2- Horizontal flat list to show the buttons 

*/

const RenderItem = memo(
  ({
    item,
    setSelectedCategory,
    isActive,
    isArabic,
  }: {
    item: ICategory;
    setSelectedCategory: (id: string) => void;
    isActive: boolean;
    isArabic: boolean;
  }) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePress = useCallback(() => {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      setSelectedCategory(item._id);
    }, [item._id, setSelectedCategory, scaleAnim]);

    const isAllButton = item._id === 'all';

    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          style={[
            styles.button,
            isActive && styles.active,
            isAllButton && styles.allButton,
            !isActive && styles.inactive,
          ]}
          onPress={handlePress}
          activeOpacity={0.7}
        >
          {item.icon && (
            <Image
              source={{ uri: item.icon }}
              style={[
                styles.icon,
                isActive && styles.activeIcon,
                !isActive && styles.inactiveIcon,
              ]}
              cachePolicy='memory-disk'
            />
          )}
          <CustomText
            style={[
              styles.text,
              isActive && styles.activeText,
              !isActive && styles.inactiveText,
              isAllButton && styles.allText,
            ]}
          >
            {isArabic ? item.arabic : item.english}
          </CustomText>
        </TouchableOpacity>
      </Animated.View>
    );
  },
);

const CategoriesButtonsList = () => {
  const { addCategory } = useCategoriesStore();
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const { isArabic } = useLanguage();

  // API CALL Hook to  get categories from backend
  const { data } = useCategoriesQuery();

  const renderList = useMemo(
    () => [
      {
        _id: 'all',
        arabic: 'الكل',
        english: 'All',
      },
      ...(data?.categories || []),
    ],
    [data],
  );

  const renderItem = useCallback(
    ({ item }: { item: ICategory }) => {
      const isActive = selectedCategory === item._id;
      return (
        <RenderItem
          item={item}
          setSelectedCategory={setSelectedCategory}
          isActive={isActive}
          isArabic={isArabic}
        />
      );
    },
    [selectedCategory, isArabic],
  );

  // IF data returned from backend it's added to store to be lifted up over all the app
  useEffect(() => {
    if (data?.categories?.length) {
      // Dispatch category
      addCategory(data.categories);
    }
  }, [data?.categories]);

  return (
    <FlatList
      style={styles.container}
      data={renderList}
      keyExtractor={(item) => item._id}
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.fatalistContent}
      renderItem={renderItem}
      windowSize={5}
      initialNumToRender={8}
      maxToRenderPerBatch={8}
      ItemSeparatorComponent={() => <View style={styles.separator} />}
    />
  );
};

export default memo(CategoriesButtonsList);

const styles = StyleSheet.create({
  container: {
    maxHeight: normalized(50),
  },
  fatalistContent: {
    paddingHorizontal: normalized(8),
    paddingVertical: normalized(4),
  },
  button: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 14,
    borderWidth: 1,
    borderRadius: 30,
    gap: 4,
  },
  inactive: {
    backgroundColor: 'transparent',
    borderColor: COLORS.neutral[400],
  },
  active: {
    backgroundColor: COLORS.primary[50],
    borderColor: COLORS.primary[50],
    shadowColor: COLORS.primary[50],
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  allButton: {
    borderColor: COLORS.neutral[400],
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  inactiveText: {
    color: COLORS.neutral[400],
  },
  activeText: {
    color: COLORS.white[50],
    fontWeight: '700',
  },
  allText: {
    color: COLORS.neutral[300],
  },
  icon: {
    width: normalized(18),
    height: normalized(18),
  },
  inactiveIcon: {
    opacity: 0.6,
    tintColor: COLORS.neutral[400],
  },
  activeIcon: {
    opacity: 1,
    tintColor: COLORS.white[50],
  },
  separator: {
    width: normalized(8),
  },
});
