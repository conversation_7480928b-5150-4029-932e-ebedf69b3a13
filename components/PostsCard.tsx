import React, { memo, ReactNode } from 'react';
import {
  Pressable,
  TouchableOpacity,
  View,
  StyleSheet,
  ViewStyle,
  ImageStyle,
  TextStyle,
} from 'react-native';
import Avatar from './ui/Avatar';
import { COLORS, normalized, SCREEN_HEIGHT } from '@/constants/Theme';
import CustomText from './ui/CustomText';
import { Image } from 'expo-image';
import { AntDesign, Entypo, Feather, Ionicons } from '@expo/vector-icons';
import { APP_Icons } from '@/constants/Images';
import CustomDropDownMenu from './ui/CustomDropDownMenu';
import { router } from 'expo-router';

// TODO Clean this component by seprating the like and unlike logic in zustand and make it's own state  in septate component

interface DropDownItem {
  key: string;
  title: string;
}

interface IPostCardProps {
  // User info
  avatar?: string;
  name: string;
  location?: string;
  userId?: string;

  // Post content
  cover: string;
  title: string;
  images?: string[];

  // Social circle specific
  socialCircle?: string;
  createdAt?: string;
  likesCount?: number;
  commentsCount?: number;

  // Interaction flags
  isLiked?: boolean;
  isPostOwner?: boolean;

  // Callbacks
  onPress?: () => void;
  onLikePress?: () => void;
  onCommentPress?: () => void;
  onSharePress?: () => void;
  showImageModal?: () => void;
  onSelect?: (e: string) => void;

  // Styling
  style?: ViewStyle;
  imageStyle?: ImageStyle;
  textStyles?: {
    name?: TextStyle;
    location?: TextStyle;
    title?: TextStyle;
    socialCircle?: TextStyle;
    createdAt?: TextStyle;
    counts?: TextStyle;
  };

  // Theme variant
  variant?: 'light' | 'dark' | 'circle';

  // Additional content
  children?: ReactNode;
}

const DropDownMenu: DropDownItem[] = [
  { key: 'report', title: 'Report Post' },
  { key: 'delete', title: 'Delete Post' },
];

const _iconsSize = 20;

const PostsCard: React.FC<IPostCardProps> = (props) => {
  const {
    avatar,
    name,
    location,
    userId,
    cover,
    title,
    images,
    socialCircle,
    createdAt,
    likesCount,
    commentsCount,
    isLiked,
    isPostOwner,
    onPress,
    onLikePress,
    onCommentPress,
    onSharePress,
    showImageModal,
    onSelect,
    style,
    imageStyle,
    textStyles,
    variant = 'light',
    children,
  } = props;

  // Determine styles based on variant
  const isCircle = variant === 'circle';
  const isDark = variant === 'dark' || isCircle;

  const _IconsColor = isDark ? COLORS.white[50] : '#797596';
  const backgroundColor = isCircle
    ? 'transparent'
    : isDark
      ? COLORS.neutral[800]
      : COLORS.white[50];
  const textColor = isDark ? COLORS.white[50] : COLORS.neutral[800];
  const secondaryTextColor = isDark ? COLORS.neutral[300] : COLORS.neutral[700];

  // Render the Circle variant
  if (isCircle) {
    return (
      <View style={[styles.circleContainer, style]}>
        <View className='px-3 py-4 gap-2'>
          {/* Header with user info */}
          <View className='flex-row items-center justify-between'>
            <Pressable
              onPress={() =>
                userId &&
                router.push({
                  pathname: '/(protected)/(tabs)/profile',
                  params: { id: userId },
                })
              }
              className='flex-row gap-3'
            >
              <Avatar
                source={{ uri: avatar }}
                text={name || '?'}
                size={normalized(20)}
              />
              <View className='gap-1'>
                <CustomText className='text-white-50 font-bold text-h5'>
                  {name}
                </CustomText>
                {createdAt && (
                  <CustomText className='text-neutral-100 font-medium text-body2'>
                    {createdAt}
                  </CustomText>
                )}
              </View>
            </Pressable>

            {socialCircle && (
              <View className='bg-secondary-300 px-4 h-8 rounded-3xl justify-center items-center'>
                <CustomText className='text-white-50 font-medium'>
                  {socialCircle}
                </CustomText>
              </View>
            )}
          </View>

          {/* Post title */}
          {title && (
            <View>
              <CustomText
                numberOfLines={2}
                className='text-white-50 text-body2 px-3'
              >
                {title}
              </CustomText>
            </View>
          )}
        </View>

        {/* Post image */}
        {cover && (
          <View className='relative'>
            <Image
              source={{ uri: cover }}
              style={[styles.circleImage, imageStyle]}
            />
            {images && images.length > 1 && (
              <Pressable
                onPress={showImageModal}
                className='absolute top-3 right-2 z-10 bg-black/30 rounded-full p-2 w-12 h-12 justify-center items-center border border-neutral-50/20'
              >
                <APP_Icons.GalleryIcon width={30} height={30} />
              </Pressable>
            )}
          </View>
        )}

        {/* Action buttons */}
        <View>
          <View className='flex-row justify-between items-center p-3'>
            <View className='flex-row items-center gap-x-4'>
              {/* Likes */}
              <TouchableOpacity
                onPress={onLikePress}
                className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'
              >
                {likesCount !== undefined && (
                  <CustomText style={styles.circleCounters}>
                    {likesCount}
                  </CustomText>
                )}
                <AntDesign
                  name={isLiked ? 'heart' : 'hearto'}
                  size={_iconsSize}
                  color={isLiked ? COLORS.danger : COLORS.white[50]}
                />
              </TouchableOpacity>

              {/* Comments */}
              <TouchableOpacity
                onPress={onCommentPress}
                className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'
              >
                {commentsCount !== undefined && (
                  <CustomText style={styles.circleCounters}>
                    {commentsCount}
                  </CustomText>
                )}
                <Feather
                  name='message-circle'
                  size={_iconsSize}
                  color={COLORS.white[50]}
                />
              </TouchableOpacity>

              {/* Share */}
              <TouchableOpacity
                className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'
                onPress={onSharePress}
              >
                <Ionicons
                  name='paper-plane-outline'
                  size={_iconsSize}
                  color={COLORS.white[50]}
                />
              </TouchableOpacity>
            </View>

            {/* Dropdown menu */}
            <CustomDropDownMenu
              items={
                !isPostOwner
                  ? DropDownMenu.filter((v) => v.key !== 'delete')
                  : DropDownMenu
              }
              onSelect={(e) => onSelect && onSelect(e)}
              triggerElement={
                <TouchableOpacity className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'>
                  <Entypo
                    name='dots-three-vertical'
                    size={_iconsSize / 1.5}
                    color={COLORS.white[50]}
                  />
                </TouchableOpacity>
              }
            />
          </View>
        </View>

        {/* Additional content */}
        {children && (
          <View>
            {typeof children === 'string' ? (
              <CustomText>{children}</CustomText>
            ) : (
              children
            )}
          </View>
        )}
      </View>
    );
  }

  // Render the Light/Dark variant
  return (
    <View style={[styles.container, { backgroundColor }, style]}>
      {/* Header with user info */}
      <View style={styles.header}>
        <Pressable
          onPress={() =>
            userId &&
            router.push({
              pathname: '/(protected)/(tabs)/profile',
              params: { id: userId },
            })
          }
        >
          <Avatar text={name || '?'} source={{ uri: avatar }} size={32} />
        </Pressable>
        <View style={styles.headerTextContainer}>
          <CustomText
            style={[styles.name, { color: textColor }, textStyles?.name]}
          >
            {name}
          </CustomText>

          {location && (
            <CustomText
              style={[
                styles.location,
                { color: secondaryTextColor },
                textStyles?.location,
              ]}
            >
              {location}
            </CustomText>
          )}

          {/* Social Circle variant shows social circle name and time */}
          {socialCircle && (
            <View style={styles.socialInfo}>
              <CustomText
                style={[
                  styles.socialCircle,
                  { color: secondaryTextColor },
                  textStyles?.socialCircle,
                ]}
              >
                {socialCircle}
              </CustomText>

              {createdAt && (
                <CustomText
                  style={[
                    styles.createdAt,
                    { color: secondaryTextColor },
                    textStyles?.createdAt,
                  ]}
                >
                  {createdAt}
                </CustomText>
              )}
            </View>
          )}
        </View>
      </View>

      {/* Post content */}
      <CustomText
        style={[styles.title, { color: textColor }, textStyles?.title]}
      >
        {title}
      </CustomText>

      {/* Post image */}
      <Pressable onPress={onPress} style={styles.imageContainer}>
        <Image source={{ uri: cover }} style={[styles.image, imageStyle]} />
        {images && images.length > 1 && (
          <Pressable onPress={showImageModal} style={styles.galleryIcon}>
            <APP_Icons.GalleryIcon width={30} height={30} />
          </Pressable>
        )}
      </Pressable>

      {/* Social stats display (only for social circle posts) */}
      {(likesCount !== undefined || commentsCount !== undefined) && (
        <View style={styles.statsContainer}>
          {likesCount !== undefined && (
            <CustomText
              style={[
                styles.statsText,
                { color: secondaryTextColor },
                textStyles?.counts,
              ]}
            >
              {likesCount} {likesCount === 1 ? 'Like' : 'Likes'}
            </CustomText>
          )}

          {commentsCount !== undefined && (
            <CustomText
              style={[
                styles.statsText,
                { color: secondaryTextColor },
                textStyles?.counts,
              ]}
            >
              {commentsCount} {commentsCount === 1 ? 'Comment' : 'Comments'}
            </CustomText>
          )}
        </View>
      )}

      {/* Action buttons */}
      <View style={styles.actions}>
        <View style={styles.actionButtons}>
          <TouchableOpacity onPress={onLikePress}>
            <AntDesign
              name={isLiked ? 'heart' : 'hearto'}
              size={normalized(25)}
              color={isLiked ? COLORS.danger : _IconsColor}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={onCommentPress}>
            <Feather
              name='message-circle'
              size={normalized(25)}
              color={_IconsColor}
            />
          </TouchableOpacity>

          <TouchableOpacity onPress={onSharePress}>
            <Ionicons
              name='paper-plane-outline'
              size={normalized(25)}
              color={_IconsColor}
            />
          </TouchableOpacity>
        </View>

        <CustomDropDownMenu
          items={
            !isPostOwner
              ? DropDownMenu.filter((v) => v.key !== 'delete')
              : DropDownMenu
          }
          onSelect={(e) => onSelect?.(e)}
          triggerElement={
            <TouchableOpacity>
              <Entypo
                name='dots-three-vertical'
                size={_iconsSize / 1.5}
                color={_IconsColor}
              />
            </TouchableOpacity>
          }
        />
      </View>

      {/* Additional content (like comments) */}
      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  // Base styles for light/dark variants
  container: {
    borderRadius: 24,
    paddingVertical: 16,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 12,
    paddingBottom: 8,
  },
  headerTextContainer: {
    flex: 1,
  },
  name: {
    fontWeight: 'bold',
    fontSize: 16,
    color: COLORS.neutral[300],
  },
  location: {
    fontSize: 12,
    fontWeight: '600',
  },
  socialInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: 2,
  },
  socialCircle: {
    fontSize: 14,
    fontWeight: '600',
  },
  createdAt: {
    fontSize: 12,
    fontWeight: '400',
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
    paddingHorizontal: 12,
    marginBottom: 4,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    height: (SCREEN_HEIGHT / 2) * 0.4,
    width: '100%',
    objectFit: 'contain',
    backgroundColor: COLORS.neutral[300],
  },
  galleryIcon: {
    position: 'absolute',
    top: 12,
    right: 8,
    zIndex: 10,
    backgroundColor: 'rgba(0,0,0,0.3)',
    borderRadius: 50,
    padding: 8,
    width: 48,
    height: 48,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(255,255,255,0.2)',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 12,
    paddingTop: 8,
  },
  statsText: {
    fontSize: 14,
    fontWeight: '500',
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingTop: 8,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },

  // Circle variant specific styles
  circleContainer: {
    backgroundColor: 'transparent',
    borderRadius: 24,

    overflow: 'hidden',
  },
  circleImage: {
    backgroundColor: 'rgba(0,0,0,0.3)',
    height: normalized(180),
    objectFit: 'cover',
    width: '100%',
  },
  circleCounters: {
    color: COLORS.white[50],
    fontWeight: '500',
  },
});

export default memo(PostsCard);
