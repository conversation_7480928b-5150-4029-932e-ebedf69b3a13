import { View, Text, FlatList, Pressable } from 'react-native';
import React from 'react';
import { INearYouItem } from '@/types';
import ActivityCard from './ActivityCard';
import EmptyListComponent from './ui/EmptyListComponent';
import { Link } from 'expo-router';
import { getDayFromDate, getMonthFromDate } from '@/utils/formatDates';
import useGetNearYou from '@/hooks/usGetNearYou';
import Loading from '@/layouts/Loading';

const RenderItem = ({ item }: { item: INearYouItem }) => {
  return (
    <Link href={`/(protected)/activity/${item._id}`} asChild>
      <Pressable>
        <ActivityCard
          cover={item.images[0]}
          day={getDayFromDate(item.startDate)}
          location={item.location.address || ''}
          month={getMonthFromDate(item.startDate)}
          title={item.name}
          currency={item.currency}
          members={item.members}
          price={Number(item.price)}
        />
      </Pressable>
    </Link>
  );
};

const NearYouList = () => {
  const { data: nearYouData, isError, isLoading, refetch } = useGetNearYou({});

  return (
    <FlatList
      data={nearYouData?.data || []}
      keyExtractor={(item) => item._id}
      showsVerticalScrollIndicator={false}
      ItemSeparatorComponent={() => <View className='my-1.5' />}
      ListEmptyComponent={
        !isLoading ? (
          <EmptyListComponent
            title='No Activities Yet!'
            description='Please try again later'
            isError={isError}
            onRefreshPress={refetch}
          />
        ) : (
          <View className='flex-1 justify-center items-center'>
            <Loading isLoading />
          </View>
        )
      }
      renderItem={({ item }) => <RenderItem item={item} />}
    />
  );
};

export default NearYouList;
