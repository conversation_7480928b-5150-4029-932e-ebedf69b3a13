import React, { memo, useEffect, useState } from 'react';
import { Platform, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import {
  GooglePlacesAutocomplete,
  GooglePlacesAutocompleteProps,
} from 'react-native-google-places-autocomplete';
import Constants from 'expo-constants';
import { COLORS, FONT, normalized } from '@/constants/Theme';

const getApiKey = () => {
  const iosKey = process.env.EXPO_PUBLIC_MAP_API_IOS_KEY || '';
  const androidKey = process.env.EXPO_PUBLIC_MAP_API_ANDROID_KEY || '';

  // Return appropriate key with fallback
  return Platform.OS === 'ios' ? iosKey : androidKey;
};

interface GooglePlacesInputProps
  extends Partial<GooglePlacesAutocompleteProps> {
  containerStyle?: StyleProp<ViewStyle>;
  styles?: {
    textInput?: StyleProp<ViewStyle>;
    listView?: StyleProp<ViewStyle>;
    row?: StyleProp<ViewStyle>;
    container?: StyleProp<ViewStyle>;
    description?: StyleProp<ViewStyle>;
    predefinedPlacesDescription?: StyleProp<ViewStyle>;
    poweredContainer?: StyleProp<ViewStyle>;
    powered?: StyleProp<ViewStyle>;
  };
}

const GooglePlacesInput: React.FC<GooglePlacesInputProps> = ({
  placeholder = 'Search places',
  containerStyle,
  styles: propStyles,
  ...restProps
}) => {
  const [apiKey, setApiKey] = useState<string>('');

  // Get API key on component mount to avoid repeated calls
  useEffect(() => {
    setApiKey(getApiKey());
  }, []);

  // Merge styles from props with default styles
  const mergedStyles = {
    textInput: [styles.input, propStyles?.textInput],
    listView: [styles.listView, propStyles?.listView],
    row: [styles.row, propStyles?.row],
    container: propStyles?.container,
    description: propStyles?.description,
    predefinedPlacesDescription: propStyles?.predefinedPlacesDescription,
    poweredContainer: propStyles?.poweredContainer,
    powered: propStyles?.powered,
  };

  return (
    <View style={[styles.container, containerStyle]}>
      <GooglePlacesAutocomplete
        placeholder={placeholder}
        fetchDetails={true}
        onFail={(error) => {
          console.error('Google places auto complete error:', error);
        }}
        query={{
          key: apiKey,
          language: 'en',
        }}
        enablePoweredByContainer={false}
        styles={mergedStyles}
        minLength={2}
        debounce={300}
        timeout={15000}
        textInputProps={{
          returnKeyType: 'search',
        }}
        keyboardShouldPersistTaps='handled'
        {...restProps}
      />
    </View>
  );
};

export default memo(GooglePlacesInput);

const styles = StyleSheet.create({
  container: {
    zIndex: 1000,
    flex: 1,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  input: {
    backgroundColor: 'transparent', // Keeps input transparent
    color: COLORS.white[50],
    fontFamily: FONT.fontFamily,
    flex: 1,
    paddingHorizontal: normalized(5),
    zIndex: 1000,
    height: '100%',
  },
  listView: {
    backgroundColor: 'white', // White background for dropdown list
    borderRadius: 10,
    elevation: 5, // Shadow for Android
    shadowColor: '#000', // Shadow for iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    zIndex: 9999,
  },
  row: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
});
