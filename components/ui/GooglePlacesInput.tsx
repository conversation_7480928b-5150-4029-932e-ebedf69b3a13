import React, { memo } from 'react';
import { Platform, StyleProp, StyleSheet, View, ViewStyle } from 'react-native';
import {
  GooglePlacesAutocomplete,
  GooglePlacesAutocompleteProps,
} from 'react-native-google-places-autocomplete';
import Constants from 'expo-constants';
import { COLORS, FONT, normalized } from '@/constants/Theme';

const getApiKey = () => {
  return Platform.OS === 'ios'
    ? Constants.expoConfig?.extra?.MAP_API_IOS_KEY ||
        'AIzaSyBW_VfI_T-RygBp0zxIMwgV2DFKxYREDYo'
    : Constants.expoConfig?.extra?.MAP_API_ANDROID_KEY ||
        'AIzaSyDZnEGOEcPFDVsvDpSCXdURHPlS5Uvl1eQ';
};

interface GooglePlacesInputProps
  extends Partial<GooglePlacesAutocompleteProps> {
  containerStyle?: StyleProp<ViewStyle>;
}

const GooglePlacesInput: React.FC<GooglePlacesInputProps> = ({
  placeholder = 'Search places',
  containerStyle,
  ...restProps
}) => {
  return (
    <View style={[styles.container, containerStyle]}>
      <GooglePlacesAutocomplete
        placeholder={placeholder}
        fetchDetails={true}
        onFail={(error) =>
          console.error('Google places auto complete error ', error)
        }
        query={{
          key: getApiKey(),
          language: 'en',
        }}
        styles={{
          textInput: styles.input, // Transparent input
          listView: styles.listView, // White background for dropdown
          row: styles.row, // Ensures each item in the list is styled properly
        }}
        {...restProps}
      />
    </View>
  );
};

export default memo(GooglePlacesInput);

const styles = StyleSheet.create({
  container: {
    zIndex: 1000,
    flex: 1,
    width: '100%',
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  input: {
    backgroundColor: 'transparent', // Keeps input transparent
    color: COLORS.white[50],
    fontFamily: FONT.fontFamily,
    flex: 1,
    paddingHorizontal: normalized(5),
    zIndex: 1000,
    height: '100%',
  },
  listView: {
    backgroundColor: 'white', // White background for dropdown list
    borderRadius: 10,
    elevation: 5, // Shadow for Android
    shadowColor: '#000', // Shadow for iOS
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
  },
  row: {
    padding: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#ddd',
  },
});
