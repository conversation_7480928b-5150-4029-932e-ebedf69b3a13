import { TouchableOpacity, View } from 'react-native';
import React, { useCallback } from 'react';
import GoBack from '@/layouts/GoBack';
import Avatar from '../ui/Avatar';
import CustomText from '../ui/CustomText';
import CustomDropDownMenu from '../ui/CustomDropDownMenu';
import { COLORS } from '@/constants/Theme';
import { Entypo } from '@expo/vector-icons';
import { timeAgo } from '@/utils/formatDates';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { router } from 'expo-router';
import useDeleteGroupChat from '@/hooks/chatHooks/useDeleteGroupChat';

/*
Chat header carries logic for
1- visit profile 
2- report 
3- block 
4- delete group if it's group chat 
*/

type IChatHeaderProps = {
  user: {
    displayName: string;
    image: string;
    _id: string;
  };
  isOnline?: boolean;
  lastSeen?: Date;
  roomId?: string;
  isGroup: boolean;
};

const dopDownItems = [
  {
    title: 'Report',
    key: 'Report',
  },
  {
    title: 'Block',
    key: 'Block',
  },
  {
    title: 'View Profile',
    key: 'View Profile',
  },
  {
    title: 'Delete Group',
    key: 'Delete Group',
  },
];

const ChatHeader = ({
  user,
  isOnline,
  lastSeen,
  roomId,
  isGroup,
}: IChatHeaderProps) => {
  const inset = useSafeAreaInsets();

  const { mutate: deleteGroup, isPending: isDeleting } = useDeleteGroupChat();

  const handleDorpDownActions = useCallback((key: string) => {
    switch (key) {
      case 'View Profile':
        router.push({
          pathname: '/(protected)/(tabs)/profile',
          params: {
            _id: user._id,
          },
        });
        break;
      case 'Delete Group':
        deleteGroup(roomId!);
        break;
      default:
        break;
    }
  }, []);

  return (
    <View
      className='bg-primary-200 px-2 '
      style={{ paddingTop: inset.top, paddingBottom: inset.top / 3.4 }}
    >
      <View className='flex-row items-center justify-between'>
        <View className='flex-row items-center gap-4'>
          <GoBack href='/(protected)/(tabs)/messages' />

          <View className='flex-row gap-3.5 '>
            <Avatar source={{ uri: user.image }} text={user.displayName} />
            <View className='gap-0.5'>
              <CustomText className='text-neutral-100 font-medium  text-h3'>
                {user.displayName}
              </CustomText>

              {isOnline && (
                <View className='flex-row items-center gap-1.5'>
                  <View className='bg-success-300 w-2 h-2 rounded-full' />
                  <CustomText className='text-neutral-400 text-h6'>
                    Online
                  </CustomText>
                </View>
              )}

              {lastSeen && !isOnline && (
                <CustomText className='text-neutral-400 text-h6 font-light'>
                  Last seen {timeAgo(lastSeen)}
                </CustomText>
              )}
            </View>
          </View>
        </View>
        <View className='flex-row items-center'>
          <CustomDropDownMenu
            items={
              isGroup
                ? dopDownItems
                : dopDownItems.filter((v) => v.key !== 'Delete Group')
            }
            onSelect={handleDorpDownActions}
            triggerElement={
              <TouchableOpacity>
                <Entypo
                  name='dots-three-vertical'
                  size={24}
                  color={COLORS.neutral[200]}
                />
              </TouchableOpacity>
            }
          />
        </View>
      </View>
    </View>
  );
};

export default ChatHeader;
