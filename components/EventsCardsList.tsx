import React, { memo } from 'react';
import { FlatList, Pressable } from 'react-native';
import EventsCard from './EventsCard';
import EmptyList from './ui/EmptyListComponent';
import { ITopEventsItem } from '@/types';
import { formatDateDayMonthWeek } from '@/utils/formatDates';
import { router } from 'expo-router';

import useGetTopEventsQuery from '@/hooks/eventsHooks/useGetTopEvents';
import { useEventsStore } from '@/stores/useEventsStore';

interface ListItemProps {
  item: ITopEventsItem;
}

const EventsCardsList = () => {
  const { setSelectedEvent } = useEventsStore();

  const { data } = useGetTopEventsQuery(3, {
    queryKey: ['topEvents', 3],
    staleTime: 5000 * 60,
  }) as {
    data: { data: ITopEventsItem[] } | undefined;
  };

  const ListItem = memo(({ item }: ListItemProps) => {
    return (
      <Pressable
        onPress={() => {
          router.push({
            pathname: '/(protected)/events/[id]',
            params: { id: item._id },
          });
          setSelectedEvent(item);
        }}
        className='mb-5'
      >
        <EventsCard
          date={formatDateDayMonthWeek(item?.createdAt)}
          location={`${item.country} ${item.city[0]} ${item.district}`}
          image={item.images?.[0]}
          title={item.name}
        />
      </Pressable>
    );
  });

  return (
    <FlatList
      className='flex-1'
      data={data?.data || []}
      // extraData={hasData}
      keyExtractor={(item) => item._id}
      ListEmptyComponent={
        <EmptyList
          title='No Upcoming Events!'
          description='There are no events happening around you right now..'
        />
      }
      renderItem={({ item }) => <ListItem item={item} />}
      initialNumToRender={8}
      maxToRenderPerBatch={8}
    />
  );
};

export default memo(EventsCardsList);
