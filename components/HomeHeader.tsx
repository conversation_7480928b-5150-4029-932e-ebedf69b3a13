import { Pressable, StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import Avatar from './ui/Avatar';
import CustomText from './ui/CustomText';
import { APP_Icons } from '@/constants/Images';
import { router } from 'expo-router';
import { useNotification } from '@/context/NotificationsContext';
import { COLORS } from '@/constants/Theme';

interface IHomeHeaderProps {
  avatar?: string;
  name: string;
  location?: string;
  hasNotification: boolean;
  containerStyle?: string;
}

const HomeHeader = (props: IHomeHeaderProps) => {
  const { notification } = useNotification();

  const baseContainerStyle = 'justify-between flex-row items-center px-4 py-3';
  const containerStyle = props.containerStyle
    ? `${baseContainerStyle} ${props.containerStyle}`
    : baseContainerStyle;

  return (
    <View className={containerStyle}>
      {/* User Avatar and Info */}
      <View className='flex-row gap-3 flex-1 items-center'>
        <TouchableOpacity
          onPress={() => router.push('/(protected)/(tabs)/profile')}
          className='flex-shrink-0'
        >
          <Avatar size={40} source={{ uri: props.avatar }} text={props.name} />
        </TouchableOpacity>

        <View className='flex-1'>
          <Pressable
            onPress={() => router.push('/(protected)/(tabs)/profile')}
            className='mb-1'
          >
            <CustomText
              className='text-white-50 text-h4 font-bold'
              numberOfLines={1}
            >
              {props.name}
            </CustomText>
          </Pressable>

          <TouchableOpacity
            onPress={() => router.push('/(protected)/profile/addLocation')}
            className='flex-row items-center gap-1.5'
          >
            <APP_Icons.LocationIcon width={18} height={18} />
            <CustomText
              numberOfLines={1}
              className='text-gray_colors-200 text-body5 font-semibold flex-1'
            >
              {props.location || 'Add Location'}
            </CustomText>
          </TouchableOpacity>
        </View>
      </View>

      {/* Notification and Calendar Buttons */}
      <View className='flex-row items-center gap-2.5 ml-2'>
        <TouchableOpacity
          style={style.roundedButton}
          onPress={() => router.push('/(protected)/(tabs)/activities')}
        >
          <APP_Icons.WhiteCalender width={18} height={18} />
        </TouchableOpacity>

        <TouchableOpacity
          style={style.roundedButton}
          onPress={() => router.push('/(protected)/notifications')}
        >
          <APP_Icons.BellIcon width={18} height={18} />
          {notification && (
            <View className='w-2.5 h-2.5 bg-danger/90 border border-danger rounded-full -right-0.5 -top-0.5 z-10 absolute' />
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default memo(HomeHeader);

const style = StyleSheet.create({
  roundedButton: {
    width: 45,
    height: 45,
    borderRadius: 40,
    borderWidth: 1,
    borderColor: COLORS.neutral[300],
    backgroundColor: 'rgba(255,255,255,0.2)',
    position: 'relative',
    justifyContent: 'center',
    alignItems: 'center',
  },
});
