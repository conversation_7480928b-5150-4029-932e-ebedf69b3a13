import { FlatList, Pressable, View } from 'react-native';
import { memo, useEffect, useState } from 'react';
import { router } from 'expo-router';

import EmptyListComponent from '../ui/EmptyListComponent';
import { APP_Icons } from '@/constants/Images';
import { normalized } from '@/constants/Theme';
import { IPost } from '@/types';
import { timeAgo } from '@/utils/formatDates';
import CardSkelton from '../Skilltons/CardSkelton';
import { useSession } from '@/context/AuthContext';
import { useDeletePost } from '@/hooks/postsHooks/useDeletePost';
import useTogglePostsLikes from '@/hooks/postsHooks/useTogglePostsLikes';
import Loading from '@/layouts/Loading';
import PostsCard from '../PostsCard';

const EmptyComponent = () => (
  <EmptyListComponent
    title='No Posts Yet'
    description='Share your first post and spark a conversation!'
    emptyIcon={() => (
      <APP_Icons.EmptyFileIcon width={normalized(40)} height={normalized(40)} />
    )}
  />
);
type IRenderItem = {
  item: IPost;
  handleReportPost: (e: string) => void;
  isPostOwner: boolean;
  handleToggleLike: () => void;
  isLiked: boolean;
};

const RenderItem = memo((props: IRenderItem) => (
  <Pressable
    onPress={() =>
      router.push({
        pathname: `/(protected)/socialCircles/post/[id]`,
        params: {
          id: props.item?._id,
          circleId: props.item?.socialCircle?._id,
          circleImage: props.item?.socialCircle?.image,
          circleName: props.item?.socialCircle?.name,
        },
      })
    }
  >
    <PostsCard
      onLikePress={props.handleToggleLike}
      onSelect={props.handleReportPost}
      isPostOwner={props.isPostOwner}
      userId={props.item.postedByUserId._id}
      onCommentPress={() =>
        router.push({
          pathname: `/(protected)/socialCircles/post/[id]`,
          params: {
            id: props.item?._id,
            circleId: props.item?.socialCircle?._id,
            circleImage: props.item?.socialCircle?.image,
            circleName: props.item?.socialCircle?.name,
          },
        })
      }
      location={props.item.socialCircle?.name || 'Public'}
      variant='circle'
      title={props.item.content}
      createdAt={timeAgo(props.item.createdAt)}
      likesCount={props.item.likes.likesCount}
      cover={props.item.image?.[0]}
      avatar={props.item.postedByUserId.image}
      name={props.item.postedByUserId.displayName}
      socialCircle={props.item.socialCircle?.name || ''}
      isLiked={props.isLiked}
    />
  </Pressable>
));

const CirclesPostsList = ({
  data = [],
  isLoading,
  isFetchingNextPage,
  fetchNextPage,
  hasNextPage,
}: {
  data: IPost[];
  isLoading?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
  hasNextPage?: boolean;
}) => {
  const { user } = useSession();
  const [posts, setPosts] = useState<IPost[]>([]);

  const { mutate: deletePost } = useDeletePost();
  const { handleToggleLike } = useTogglePostsLikes(setPosts, posts || []);

  useEffect(() => {
    setPosts(data);
  }, [data]);

  const handlePostActions = (action: string, id: string) => {
    if (action === 'delete') {
      setPosts((prev) => prev.filter((post) => post._id !== id));
      deletePost(id);
    }
  };

  const handleEndReached = () => {
    if (hasNextPage && !isFetchingNextPage) {
      fetchNextPage?.();
    }
  };

  const renderItem = ({ item }: { item: IPost | null }) => {
    if (!item) return <CardSkelton />;
    return (
      <RenderItem
        isPostOwner={user?._id === item.postedByUserId?._id}
        handleReportPost={(e) => handlePostActions(e, item._id)}
        handleToggleLike={() => user?._id && handleToggleLike(item._id)}
        isLiked={item.likes.userId.includes(user?._id || '')}
        item={item}
      />
    );
  };

  return (
    <FlatList
      data={isLoading ? Array.from({ length: 5 }, () => null) : posts}
      className='flex-1 mb-8'
      keyExtractor={(item, index) => (item ? item._id : `skeleton-${index}`)}
      ListEmptyComponent={!isLoading ? <EmptyComponent /> : null}
      renderItem={renderItem}
      contentContainerClassName='gap-3  py-4 flex-grow'
      showsVerticalScrollIndicator={false}
      initialNumToRender={5}
      maxToRenderPerBatch={5}
      onEndReachedThreshold={0.5}
      onEndReached={handleEndReached}
      removeClippedSubviews
      inverted={false}
      ListFooterComponent={
        isFetchingNextPage ? (
          <View className='py-4'>
            <Loading isLoading />
          </View>
        ) : null
      }
    />
  );
};
export default CirclesPostsList;
