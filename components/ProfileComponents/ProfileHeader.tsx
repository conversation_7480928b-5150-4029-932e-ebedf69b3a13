import { TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import CustomHeader from '../ui/CustomHeader';
import { router } from 'expo-router';
import GearIcon from '@/assets/icons/gear.svg';
import { COLORS, normalized } from '@/constants/Theme';
import Avatar from '../ui/Avatar';
import CustomText from '../ui/CustomText';
import { Octicons } from '@expo/vector-icons';
type Props = {
  avatar: string;
  displayName: string;
  username: string;
  location: string;
};

const ProfileHeader: React.FC<Props> = ({
  avatar,
  displayName,
  location,
  username,
}) => {
  return (
    <View>
      <View className='mb-3 py-2'>
        <CustomHeader title='Profile'>
          {/* {isCurrentUser  && ( */}
          <TouchableOpacity
            className='bg-white-50/10  w-[3rem] h-[3rem] items-center justify-center rounded-full border border-neutral-800'
            onPress={() => router.push('/(protected)/profile/profileSettings')}
          >
            <GearIcon width={normalized(18)} height={normalized(18)} />
          </TouchableOpacity>
          {/* )} */}
        </CustomHeader>
      </View>

      {/* Profile Header container */}
      <View className=' flex-row'>
        {/* avatar container */}
        <View className=' items-center  flex-row   gap-2.5'>
          <Avatar
            source={{ uri: avatar }}
            text={displayName}
            size={normalized(40)}
          />
          <View>
            <CustomText
              className='text-white-50 font-semibold text-h4 '
              numberOfLines={1}
            >
              {displayName}
            </CustomText>
            <CustomText className='text-neutral-300 text-body2 mb-1'>
              @{username}
            </CustomText>
            {/* Location */}
            <View className='flex-row items-center gap-3'>
              <Octicons
                name='location'
                size={normalized(14)}
                color={COLORS.secondary[300]}
              />
              <CustomText className='text-white-50 text-body3'>
                {location || 'No Entered Location'}
              </CustomText>
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(ProfileHeader);
