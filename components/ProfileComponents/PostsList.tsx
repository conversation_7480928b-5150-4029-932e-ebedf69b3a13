import { FlatList, StyleSheet, View } from 'react-native';
import React, { memo, useEffect, useState } from 'react';
import PostsCard from '../PostsCard';
import { useRouter } from 'expo-router';
import { normalized } from '@/constants/Theme';
import EmptyListComponent from '../ui/EmptyListComponent';
import useTogglePostsLikes from '@/hooks/postsHooks/useTogglePostsLikes';
import { IPost } from '@/types';
import { useSession } from '@/context/AuthContext';
import Loading from '@/layouts/Loading';

interface IPostsList {
  userId: string;
  data: IPost[];
  isLoading?: boolean;
}

const PostsList = ({ userId, data = [], isLoading }: IPostsList) => {
  const router = useRouter();
  const { userId: currentUserId } = useSession();
  const [posts, setPosts] = useState<IPost[]>([]);

  const { handleToggleLike } = useTogglePostsLikes(setPosts, posts);

  useEffect(() => {
    // Only update state if data has actually changed
    const isSame =
      posts.length === data.length &&
      posts.every((p, i) => p._id === data[i]?._id);

    if (!isSame) {
      setPosts(data);
    }
  }, [data]);

  if (isLoading) {
    return (
      <View className='flex-1 justify-center items-center '>
        <Loading isLoading />
      </View>
    );
  }

  return (
    <FlatList
      data={posts}
      inverted={false}
      initialNumToRender={10}
      scrollEventThrottle={16}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={
        <EmptyListComponent
          title=' No posts created yet!'
          description='No Posts created By User'
        />
      }
      style={styles.listStyles}
      ItemSeparatorComponent={() => <View className='h-[1.5rem]' />}
      keyExtractor={(item, index) => item._id || index.toString()}
      renderItem={({ item }) => (
        <PostsCard
          onPress={() =>
            router.navigate(`/(protected)/profile/post/${item._id}`)
          }
          cover={item.image?.[0] || ''}
          location={item.visibility}
          name={item.postedByUserId.displayName}
          avatar={item.postedByUserId.image}
          title={item.content}
          onLikePress={() => handleToggleLike(item._id)}
          isLiked={
            item.likes?.userId?.includes(userId) ||
            item.likes?.userId?.includes(currentUserId || '')
          }
          onCommentPress={() =>
            router.push(`/(protected)/profile/post/${item._id}`)
          }
        />
      )}
    />
  );
};

export default memo(PostsList);

const styles = StyleSheet.create({
  listStyles: {
    flex: 1,
    paddingBottom: normalized(4),
    marginBottom: normalized(50),
  },
});
